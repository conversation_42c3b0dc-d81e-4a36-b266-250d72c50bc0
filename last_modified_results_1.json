[{"row_index": 2, "ip_address": "************", "url": "http://************:80", "port": 80, "last_modified": "2013-08-07T04:17:14+00:00", "service_version": 1.1, "application_name": "[\"ASP.NET\", \"Microsoft IIS Web服务器\", \"微软SharePoint\", \"ASP\", \"Windows\"]", "org": NaN, "country": "中国"}, {"row_index": 3, "ip_address": "*************", "url": "http://*************:80", "port": 80, "last_modified": "2020-03-09T11:03:44+00:00", "service_version": 1.1, "application_name": "[\"ASP\", \"微软SharePoint\", \"ASP.NET\", \"Microsoft IIS Web服务器\", \"Windows\"]", "org": NaN, "country": "中国"}, {"row_index": 18, "ip_address": "************", "url": "http://sts3.hhhs.tp.edu.tw:80", "port": 80, "last_modified": "2013-08-07T04:17:14+00:00", "service_version": 1.1, "application_name": "[\"Microsoft IIS Web服务器\", \"微软SharePoint\", \"ASP\", \"ASP.NET\", \"Windows\"]", "org": NaN, "country": "中国"}, {"row_index": 30, "ip_address": "**************", "url": "http://www.macronix.com:80", "port": 80, "last_modified": "2025-07-19T21:25:00+00:00", "service_version": 1.1, "application_name": "[\"Aethra-Telecommunications-OS\", \"微软SharePoint\", \"Font Awesome\", \"jQuery\", \"Citrix-NetScaler-Gateway\", \"KYOCERA-ECOSYS\", \"Google Hosted Libraries\", \"analytics.js\", \"Windows 10\", \"Windows Server 2016\"]", "org": NaN, "country": "中国"}, {"row_index": 31, "ip_address": "************", "url": "https://eimember.eximbank.com.tw:443", "port": 443, "last_modified": "2025-07-18T20:48:37+00:00", "service_version": 1.1, "application_name": "[\"微软SharePoint\", \"Bootstrap\", \"jQuery\", \"PHP\", \"jQuery-ui\", \"Azure-ARR\"]", "org": "[\"The Export-Import Bank of the Republic of China\"]", "country": "中国"}, {"row_index": 33, "ip_address": "**************", "url": "https://www.mxic.com.tw:443", "port": 443, "last_modified": "2025-07-18T06:42:23+00:00", "service_version": 1.1, "application_name": "[\"Google Hosted Libraries\", \"KYOCERA-ECOSYS\", \"analytics.js\", \"Bootstrap\", \"Citrix-NetScaler-Gateway\", \"微软SharePoint\", \"Font Awesome\", \"jQuery\", \"openSUSE\"]", "org": "[\"MACRONIX INTERNATIONAL CO., LTD.\"]", "country": "中国"}, {"row_index": 36, "ip_address": "************", "url": "https://ei.eximbank.com.tw:443", "port": 443, "last_modified": "2025-07-07T03:24:27+00:00", "service_version": 1.1, "application_name": "[\"微软SharePoint\", \"PHP\", \"Bootstrap\", \"jQuery\"]", "org": "[\"The Export-Import Bank of the Republic of China\"]", "country": "中国"}, {"row_index": 47, "ip_address": "*************", "url": "http://*************:6666", "port": 6666, "last_modified": "2025-06-19T04:42:51+00:00", "service_version": 1.1, "application_name": "[\"ASP\", \"ASP.NET\", \"Microsoft IIS Web服务器\", \"微软SharePoint\", \"Windows\"]", "org": NaN, "country": "中国"}, {"row_index": 49, "ip_address": "**************", "url": "https://www.macronix.com.hk:443", "port": 443, "last_modified": "2025-06-16T06:44:55+00:00", "service_version": 1.1, "application_name": "[\"jQuery\", \"openSUSE\", \"微软SharePoint\", \"analytics.js\"]", "org": "[\"MACRONIX INTERNATIONAL CO., LTD.\"]", "country": "中国"}, {"row_index": 62, "ip_address": "**************", "url": "http://www.mxic.com.tw:80", "port": 80, "last_modified": "2025-04-30T00:30:15+00:00", "service_version": 1.1, "application_name": "[\"微软SharePoint\", \"Font Awesome\", \"jQuery\", \"Citrix-NetScaler-Gateway\", \"openSUSE\", \"KYOCERA-ECOSYS\", \"Google Hosted Libraries\", \"analytics.js\"]", "org": NaN, "country": "中国"}, {"row_index": 65, "ip_address": "*************", "url": "http://princo-wss2.princo.com.tw:6666", "port": 6666, "last_modified": "2025-04-21T11:43:00+00:00", "service_version": 1.1, "application_name": "[\"ASP\", \"ASP.NET\", \"Microsoft IIS Web服务器\", \"微软SharePoint\", \"Windows\"]", "org": NaN, "country": "中国"}, {"row_index": 69, "ip_address": "***********", "url": "http://academic2.wyes.tn.edu.tw:80", "port": 80, "last_modified": "2025-04-07T13:53:01+00:00", "service_version": 1.1, "application_name": "[\"Microsoft IIS Web服务器\", \"微软SharePoint\", \"ASP\", \"ASP.NET\", \"Windows\"]", "org": NaN, "country": "中国"}, {"row_index": 71, "ip_address": "**************", "url": "https://desjardinslifeinsurance.com:443", "port": 443, "last_modified": "2025-02-12T17:15:30+00:00", "service_version": 1.1, "application_name": "[\"<PERSON><PERSON><PERSON><PERSON>t Manager\", \"微软SharePoint\"]", "org": "[\"<PERSON><PERSON><PERSON> Desjardins\"]", "country": "中国"}, {"row_index": 72, "ip_address": "************", "url": "http://iaio-aio.com:80", "port": 80, "last_modified": "2024-05-21T10:03:40+00:00", "service_version": NaN, "application_name": "[\"Nginx Web服务器\", \"Bootstrap\", \"jQuery\", \"微软SharePoint\"]", "org": NaN, "country": "中国"}, {"row_index": 74, "ip_address": "************", "url": "http://www.iaio-aio.com:80", "port": 80, "last_modified": "2024-05-21T10:03:40+00:00", "service_version": NaN, "application_name": "[\"微软SharePoint\", \"jQuery\", \"Bootstrap\", \"Nginx Web服务器\"]", "org": NaN, "country": "中国"}, {"row_index": 81, "ip_address": "*************", "url": "http://www.spe.org.tw:80", "port": 80, "last_modified": "2023-01-13T01:29:18+00:00", "service_version": NaN, "application_name": "[\"ASP\", \"ASP.NET\", \"Microsoft IIS Web服务器\", \"微软SharePoint\", \"Windows\"]", "org": NaN, "country": "中国"}, {"row_index": 82, "ip_address": "*************", "url": "http://ir.ntua.edu.tw:80", "port": 80, "last_modified": "2023-12-17T04:02:13+00:00", "service_version": NaN, "application_name": "[\"微软SharePoint\", \"ASP\", \"ASP.NET\", \"Microsoft IIS Web服务器\", \"Windows\"]", "org": NaN, "country": "中国"}, {"row_index": 88, "ip_address": "***************", "url": "http://***************:80", "port": 80, "last_modified": "2023-09-26T16:57:07+00:00", "service_version": NaN, "application_name": "[\"微软SharePoint\", \"Microsoft IIS Web服务器\", \"ASP\", \"ASP.NET\", \"PHP\", \"Windows Server 2008\"]", "org": NaN, "country": "中国"}, {"row_index": 89, "ip_address": "***************", "url": "http://***************:80", "port": 80, "last_modified": "2023-09-26T16:56:40+00:00", "service_version": NaN, "application_name": "[\"ASP\", \"ASP.NET\", \"微软SharePoint\", \"Microsoft IIS Web服务器\", \"PHP\", \"Windows Server 2008\"]", "org": NaN, "country": "中国"}, {"row_index": 93, "ip_address": "*************", "url": "http://*************:80", "port": 80, "last_modified": "2023-08-30T20:24:35+00:00", "service_version": NaN, "application_name": "[\"微软SharePoint\", \"Microsoft IIS Web服务器\", \"ASP\", \"ASP.NET\", \"Windows Server 2012 R2\"]", "org": NaN, "country": "中国"}, {"row_index": 94, "ip_address": "**************", "url": "https://collaborationsharepoint.blogspot.com:443", "port": 443, "last_modified": "2023-03-19T13:08:11+00:00", "service_version": NaN, "application_name": "[\"1781A_GSEy-Login\", \"OpenGSE\", \"Lightbox\", \"KindEditor\"]", "org": NaN, "country": "中国"}, {"row_index": 95, "ip_address": "**************", "url": "http://router.etc.org.tw:80", "port": 80, "last_modified": "2023-07-25T06:56:08+00:00", "service_version": NaN, "application_name": "[\"ASP\", \"ASP.NET\", \"微软SharePoint\", \"analytics.js\", \"jQuery\", \"Google-站长平台\", \"Microsoft IIS Web服务器\"]", "org": NaN, "country": "中国"}, {"row_index": 97, "ip_address": "***************", "url": "https://moss.isu.edu.tw:443", "port": 443, "last_modified": "2023-06-20T11:50:59+00:00", "service_version": NaN, "application_name": "[\"ASP\", \"ASP.NET\", \"微软SharePoint\", \"PHP\", \"Microsoft IIS Web服务器\", \"Windows\"]", "org": "[\"I-Shou University\"]", "country": "中国"}, {"row_index": 99, "ip_address": "*************", "url": "https://ir.ntua.edu.tw:443", "port": 443, "last_modified": "2023-05-29T15:20:50+00:00", "service_version": NaN, "application_name": "[\"微软SharePoint\", \"ASP\", \"ASP.NET\", \"Microsoft IIS Web服务器\", \"Windows\"]", "org": "[\"National Taiwan University of Arts\"]", "country": "中国"}, {"row_index": 100, "ip_address": "*************", "url": "https://*************:443", "port": 443, "last_modified": "2022-09-05T10:15:16+00:00", "service_version": NaN, "application_name": "[\"jQuery\", \"微软SharePoint\", \"InfoPro-System\", \"JAVA\", \"jQuery-ui\", \"Microsoft-Exchange\"]", "org": "[\"Industrial and Commercial Bank of China Limited\"]", "country": "中国"}, {"row_index": 101, "ip_address": "*************", "url": "https://*************:443", "port": 443, "last_modified": "2022-09-05T10:15:16+00:00", "service_version": NaN, "application_name": "[\"jQuery\", \"微软SharePoint\", \"InfoPro-System\", \"JAVA\", \"jQuery-ui\", \"Microsoft-Exchange\"]", "org": "[\"Industrial and Commercial Bank of China Limited\"]", "country": "中国"}, {"row_index": 102, "ip_address": "**************", "url": "http://**************:80", "port": 80, "last_modified": "2022-09-05T10:15:16+00:00", "service_version": NaN, "application_name": "[\"Microsoft-Exchange\", \"jQuery\", \"微软SharePoint\", \"InfoPro-System\", \"JAVA\", \"jQuery-ui\"]", "org": NaN, "country": "中国"}, {"row_index": 103, "ip_address": "**************", "url": "https://**************:443", "port": 443, "last_modified": "2022-09-05T10:15:16+00:00", "service_version": NaN, "application_name": "[\"jQuery\", \"微软SharePoint\", \"InfoPro-System\", \"JAVA\", \"jQuery-ui\", \"Microsoft-Exchange\"]", "org": "[\"Industrial and Commercial Bank of China Limited\"]", "country": "中国"}, {"row_index": 104, "ip_address": "*************", "url": "http://*************:80", "port": 80, "last_modified": "2022-09-05T10:15:16+00:00", "service_version": NaN, "application_name": "[\"jQuery\", \"微软SharePoint\", \"InfoPro-System\", \"JAVA\", \"jQuery-ui\", \"Microsoft-Exchange\"]", "org": NaN, "country": "中国"}, {"row_index": 105, "ip_address": "*************", "url": "http://*************:80", "port": 80, "last_modified": "2022-09-05T10:15:16+00:00", "service_version": NaN, "application_name": "[\"jQuery\", \"微软SharePoint\", \"InfoPro-System\", \"JAVA\", \"jQuery-ui\", \"Microsoft-Exchange\"]", "org": NaN, "country": "中国"}, {"row_index": 108, "ip_address": "*************", "url": "http://60-249-132-21.hinet-ip.hinet.net:80", "port": 80, "last_modified": "2020-03-09T11:03:44+00:00", "service_version": NaN, "application_name": "[\"Microsoft IIS Web服务器\", \"微软SharePoint\", \"Kedacom-Cameras-and-Surveillance\", \"ASP\", \"ASP.NET\", \"Windows\"]", "org": NaN, "country": "中国"}, {"row_index": 110, "ip_address": "*************", "url": "https://*************:443", "port": 443, "last_modified": "2017-12-14T02:55:23+00:00", "service_version": NaN, "application_name": "[\"Microsoft IIS Web服务器\", \"ASP\", \"ASP.NET\", \"微软SharePoint\", \"Windows\"]", "org": "[\"高雄市政府\"]", "country": "中国"}, {"row_index": 116, "ip_address": "*************", "url": "http://*************:80", "port": 80, "last_modified": "2022-10-30T20:00:29+00:00", "service_version": NaN, "application_name": "[\"Microsoft IIS Web服务器\", \"微软SharePoint\", \"ASP\", \"Windows\"]", "org": NaN, "country": "中国"}, {"row_index": 117, "ip_address": "*************", "url": "http://*************:80", "port": 80, "last_modified": "2022-05-17T09:14:08+00:00", "service_version": NaN, "application_name": "[\"Microsoft-Exchange\", \"jQuery\", \"微软SharePoint\", \"JAVA\", \"jQuery-ui\"]", "org": NaN, "country": "中国"}, {"row_index": 118, "ip_address": "**************", "url": "http://**************:80", "port": 80, "last_modified": "2022-05-17T09:14:08+00:00", "service_version": NaN, "application_name": "[\"Microsoft-Exchange\", \"jQuery\", \"微软SharePoint\", \"JAVA\", \"jQuery-ui\"]", "org": NaN, "country": "中国"}, {"row_index": 119, "ip_address": "************", "url": "http://1-34-245-139.hinet-ip.hinet.net:80", "port": 80, "last_modified": "2022-08-03T03:16:50+00:00", "service_version": NaN, "application_name": "[\"微软SharePoint\", \"Microsoft IIS Web服务器\", \"ASP\", \"Windows\"]", "org": NaN, "country": "中国"}, {"row_index": 120, "ip_address": "*************", "url": "http://webmail.industries-tax.org.tw:80", "port": 80, "last_modified": "2022-08-03T02:59:40+00:00", "service_version": NaN, "application_name": "[\"微软SharePoint\", \"ASP\", \"Microsoft IIS Web服务器\", \"Windows\"]", "org": NaN, "country": "中国"}, {"row_index": 121, "ip_address": "*************", "url": "http://211-23-194-92.hinet-ip.hinet.net:80", "port": 80, "last_modified": "2022-08-03T02:59:43+00:00", "service_version": NaN, "application_name": "[\"微软SharePoint\", \"ASP\", \"Microsoft IIS Web服务器\", \"Windows\"]", "org": NaN, "country": "中国"}, {"row_index": 122, "ip_address": "**************", "url": "http://www.starone.tw:80", "port": 80, "last_modified": "2022-08-03T02:57:54+00:00", "service_version": NaN, "application_name": "[\"ASP\", \"Microsoft IIS Web服务器\", \"微软SharePoint\", \"Windows\"]", "org": NaN, "country": "中国"}, {"row_index": 123, "ip_address": "*************", "url": "http://www.tudocat.com:80", "port": 80, "last_modified": "2022-08-03T02:50:22+00:00", "service_version": NaN, "application_name": "[\"微软SharePoint\", \"ASP\", \"jQuery\", \"PHP\", \"Microsoft IIS Web服务器\", \"Windows\"]", "org": NaN, "country": "中国"}, {"row_index": 124, "ip_address": "**************", "url": "http://sps.bgjd.org.tw:80", "port": 80, "last_modified": "2022-08-03T02:45:26+00:00", "service_version": NaN, "application_name": "[\"Nginx Web服务器\", \"ASP\", \"微软SharePoint\", \"PHP\", \"analytics.js\", \"cPanel虚拟主机管理系统\", \"Windows\"]", "org": NaN, "country": "中国"}, {"row_index": 125, "ip_address": "**************", "url": "http://www.aiti.org.tw:80", "port": 80, "last_modified": "2022-08-02T12:36:21+00:00", "service_version": NaN, "application_name": "[\"微软SharePoint\", \"ASP\", \"Microsoft IIS Web服务器\"]", "org": NaN, "country": "中国"}, {"row_index": 128, "ip_address": "************", "url": "https://************:443", "port": 443, "last_modified": "2021-08-20T17:28:39+00:00", "service_version": NaN, "application_name": "[\"Node.js\", \"微软SharePoint\", \"Nginx Web服务器\", \"Express\", \"Node.js Express framework\"]", "org": NaN, "country": "中国"}, {"row_index": 132, "ip_address": "***********", "url": "http://eimember.eximbank.com.tw:80", "port": 80, "last_modified": "2022-04-26T01:39:10+00:00", "service_version": NaN, "application_name": "[\"Microsoft IIS Web服务器\", \"微软SharePoint\", \"Bootstrap\", \"jQuery\", \"analytics.js\", \"PHP\", \"Windows\"]", "org": NaN, "country": "中国"}, {"row_index": 135, "ip_address": "***************", "url": "http://repo.asia.edu.tw:80", "port": 80, "last_modified": "2022-04-23T22:46:13+00:00", "service_version": NaN, "application_name": "[\"微软SharePoint\", \"ASP\", \"Microsoft IIS Web服务器\", \"Windows\"]", "org": NaN, "country": "中国"}, {"row_index": 136, "ip_address": "***************", "url": "https://repo.asia.edu.tw:443", "port": 443, "last_modified": "2022-04-23T22:37:55+00:00", "service_version": NaN, "application_name": "[\"微软SharePoint\", \"ASP\", \"Microsoft IIS Web服务器\", \"Windows\"]", "org": "[\"Asia University\"]", "country": "中国"}, {"row_index": 143, "ip_address": "***********", "url": "http://***********:80", "port": 80, "last_modified": "2011-05-18T15:02:51+00:00", "service_version": NaN, "application_name": "[\"微软SharePoint\", \"Microsoft IIS Web服务器\", \"ASP\", \"Windows\"]", "org": NaN, "country": "中国"}, {"row_index": 144, "ip_address": "***************", "url": "http://***************:80", "port": 80, "last_modified": "2003-07-27T14:45:58+00:00", "service_version": NaN, "application_name": "[\"ASP\", \"Microsoft IIS Web服务器\", \"微软SharePoint\"]", "org": NaN, "country": "中国"}]