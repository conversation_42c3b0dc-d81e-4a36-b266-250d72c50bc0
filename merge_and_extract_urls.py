#!/usr/bin/env python3
"""
合并1.xlsx和2.xlsx文件，提取URL信息
如果URL字段不为空就提取域名，否则根据IP、端口、服务名称构建URL
输出到txt文件，每行一个URL
"""

import pandas as pd
import sys
from urllib.parse import urlparse
import re

def extract_domain_from_url(url):
    """从完整URL中提取域名"""
    try:
        parsed = urlparse(url)
        return parsed.netloc
    except:
        return None

def build_url_from_service(ip, port, service):
    """根据IP、端口、服务名称构建URL"""
    if pd.isna(ip) or pd.isna(port):
        return None
    
    # 清理IP地址
    ip = str(ip).strip()
    port = str(int(float(port)))  # 转换端口号
    
    # 判断协议
    if pd.isna(service):
        protocol = "http"
    else:
        service_str = str(service).lower()
        if "ssl" in service_str or "https" in service_str:
            protocol = "https"
        else:
            protocol = "http"
    
    return f"{protocol}://{ip}:{port}"

def merge_and_extract_urls():
    try:
        # 读取两个Excel文件
        print("Reading 1.xlsx...")
        df1 = pd.read_excel('1.xlsx')
        print(f"1.xlsx: {len(df1)} records")
        
        print("Reading 2.xlsx...")
        df2 = pd.read_excel('2.xlsx')
        print(f"2.xlsx: {len(df2)} records")
        
        # 合并数据
        print("Merging dataframes...")
        df = pd.concat([df1, df2], ignore_index=True)
        print(f"Total records after merge: {len(df)}")
        
        # 查找URL相关列
        url_column = None
        for col in df.columns:
            if 'URL' in col or 'url' in col:
                url_column = col
                break
        
        print(f"Found URL column: {url_column}")
        
        # 提取URL
        urls = []
        
        for idx, row in df.iterrows():
            if idx % 1000 == 0:
                print(f"Processing: {idx}/{len(df)}")
            
            url = None
            
            # 首先检查URL字段
            if url_column and not pd.isna(row[url_column]):
                url_value = str(row[url_column]).strip()
                if url_value:
                    # 提取域名
                    domain = extract_domain_from_url(url_value)
                    if domain:
                        # 判断协议
                        if url_value.startswith('https://'):
                            url = f"https://{domain}"
                        else:
                            url = f"http://{domain}"
            
            # 如果URL字段为空，根据IP、端口、服务构建
            if not url:
                ip = row.get('IP地址')
                port = row.get('端口号')
                service = row.get('服务名称')
                
                url = build_url_from_service(ip, port, service)
            
            if url:
                urls.append(url)
        
        # 去重并排序
        unique_urls = list(set(urls))
        unique_urls.sort()
        
        print(f"\nExtracted {len(urls)} URLs")
        print(f"Unique URLs: {len(unique_urls)}")
        
        # 写入文件
        output_file = 'merged_urls.txt'
        with open(output_file, 'w', encoding='utf-8') as f:
            for url in unique_urls:
                f.write(url + '\n')
        
        print(f"URLs saved to {output_file}")
        
        # 显示前10个URL作为预览
        print("\nFirst 10 URLs:")
        for i, url in enumerate(unique_urls[:10]):
            print(f"  {url}")
        
        if len(unique_urls) > 10:
            print(f"  ... and {len(unique_urls) - 10} more URLs")
        
        return True
        
    except FileNotFoundError as e:
        print(f"Error: File not found - {e}")
        return False
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = merge_and_extract_urls()
    sys.exit(0 if success else 1)