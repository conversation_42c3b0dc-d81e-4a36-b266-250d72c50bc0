#!/usr/bin/env python3
import pandas as pd
import sys
from datetime import datetime, timezone
import os
import re
import json
def parse_last_modified(last_modified_str):
    """Parse Last-Modified header field"""
    if not last_modified_str:
        return None

    try:
        # HTTP date format: "Wed, 21 Oct 2015 07:28:00 GMT"
        return datetime.strptime(last_modified_str, "%a, %d %b %Y %H:%M:%S %Z").replace(tzinfo=timezone.utc)
    except ValueError:
        try:
            # Try other common formats
            return datetime.strptime(last_modified_str, "%a, %d %b %Y %H:%M:%S GMT").replace(tzinfo=timezone.utc)
        except ValueError:
            # scan_log.log('WARNING', f"Cannot parse Last-Modified date format: {last_modified_str}")
            return None

def is_before_target_date(last_modified):
    """Check if date is before target date"""
    target_date = datetime(2025, 7, 18, tzinfo=timezone.utc)
    return last_modified < target_date
def extract_etag_and_last_modified():
    """从citrix_target.xlsx文件的原始响应中提取Etag和Last-Modified值"""
    
    excel_file = '2.xlsx'
    
    if not os.path.exists(excel_file):
        print(f"错误: 文件 {excel_file} 不存在")
        return
    
    try:
        # 读取Excel文件
        print(f"正在读取文件: {excel_file}")
        df = pd.read_excel(excel_file)
        
        print(f"\n文件基本信息:")
        print(f"- 数据行数: {len(df)}")
        print(f"- 列数: {len(df.columns)}")
        
        # 查找原始响应列
        response_column = None
        for col in df.columns:
            if '原始响应' in col or 'response' in col.lower() or '响应' in col:
                response_column = col
                break
        
        if not response_column:
            print("未找到原始响应列，显示所有列名:")
            for i, col in enumerate(df.columns):
                print(f"{i+1}. {col}")
            return
        
        print(f"\n找到原始响应列: {response_column}")
        
        # 提取Etag和Last-Modified
        results = []
        # etag_pattern = re.compile(r'etag:\s*([^\r\n]+)', re.IGNORECASE)
        last_modified_pattern = re.compile(r'last-modified:\s*([^\r\n]+)', re.IGNORECASE)
        
        # print(f"\n开始提取Etag和Last-Modified值...")
        
        for idx, row in df.iterrows():
            if idx % 1000 == 0:
                print(f"处理进度: {idx}/{len(df)}")
            
            response_data = row[response_column]
            if pd.isna(response_data):
                continue
                
            response_str = str(response_data)
            
            # 提取Etag
            # etag_match = etag_pattern.search(response_str)
            # etag_value = etag_match.group(1).strip() if etag_match else None
            
            # 提取Last-Modified
            last_modified_match = last_modified_pattern.search(response_str)
            last_modified_value = last_modified_match.group(1).strip() if last_modified_match else None
            parsed_date = parse_last_modified(last_modified_value)
            if parsed_date:
                last_modified_final = parsed_date.isoformat()
                true_or_false = is_before_target_date(parsed_date)

                if true_or_false and row.get('主体所属组织', '') != ' ':
                    result = {
                        'row_index': idx,
                        'ip_address': row.get('IP地址', ''),
                        'url': row.get('URL', ''),
                        'port': row.get('端口号', ''),
                        'last_modified': last_modified_final,
                        'service_version': row.get('服务版本号', ''),
                        'application_name': row.get('应用名称', ''),
                        'org': row.get('主体所属组织', ''),
                        'country':row.get('国家（中文）', '')
                    }
                    results.append(result)
        
        print(f"\n提取完成!")
        print(f"总共找到 {len(results)} 条包含Last-Modified的记录")
        
        # 统计信息
        # etag_count = sum(1 for r in results if r['etag'])
        last_modified_count = sum(1 for r in results if r['last_modified'])
        
        # print(f"- 包含Etag的记录: {etag_count}")
        print(f"- 包含Last-Modified的记录: {last_modified_count}")
        
        # 显示前10条结果
        print(f"\n前10条结果:")
        for i, result in enumerate(results[:10]):
            print(f"\n{i+1}. 行 {result['row_index']}:")
            print(f"   IP: {result['ip_address']}")
            print(f"   URL: {result['url']}")
            print(f"   端口: {result['port']}")
            print(f"   应用: {result['application_name']}")
            print(f"   版本: {result['service_version']}")
            # if result['etag']:
            #     print(f"   Etag: {result['etag']}")
            if result['last_modified']:
                print(f"   Last-Modified: {result['last_modified']}")
        
        # 保存结果到文件
        output_file = 'last_modified_results_2.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"\n结果已保存到: {output_file}")
        
        # 创建CSV文件便于查看
        csv_file = 'etag_last_modified_results_2.csv'
        results_df = pd.DataFrame(results)
        results_df.to_csv(csv_file, index=False, encoding='utf-8-sig')
        print(f"结果已保存到CSV文件: {csv_file}")
        
        # 统计不同的Etag值
        # unique_etags = set()
        # for result in results:
        #     if result['etag']:
        #         unique_etags.add(result['etag'])
        
        # print(f"\n发现 {len(unique_etags)} 个不同的Etag值:")
        # for etag in sorted(unique_etags):
        #     print(f"  {etag}")
        
        return results
        
    except Exception as e:
        print(f"分析文件时出错: {e}")
        import traceback
        traceback.print_exc()
        return []

if __name__ == "__main__":
    extract_etag_and_last_modified()
