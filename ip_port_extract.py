#!/usr/bin/env python3
"""
Extract IP addresses and port numbers from citrix_target.xlsx
Only extract records where '服务原始响应' contains 'NSC_'
Output them in ip:port format to citrix_target_ip.txt
"""

import pandas as pd
import sys

def extract_ip_port():
    try:
        # Read the Excel file
        print("Reading 2.xlsx...")
        df = pd.read_excel('2.xlsx')

        # Check if required columns exist
        required_columns = ['IP地址', '端口号', '服务名称']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"Error: Required columns {missing_columns} not found in the Excel file")
            return False

        print(f"Total records in file: {len(df)}")

        # Filter records where '服务原始响应' contains 'NSC_'
        print("Filtering records where '服务原始响应' contains 'NSC_'...")
        nsc_filter = df['服务原始响应'].astype(str).str.contains('NSC_', na=False, case=False)
        filtered_df = df[nsc_filter]

        print(f"Found {len(filtered_df)} records containing 'NSC_' in '服务原始响应'")

        if len(filtered_df) == 0:
            print("No records found with 'NSC_' in '服务原始响应'")
            return False

        # Extract IP and port columns from filtered data
        ip_port_data = filtered_df[['IP地址', '端口号']].copy()

        # Remove rows with missing IP or port data
        ip_port_data = ip_port_data.dropna()

        # Remove duplicates
        ip_port_data = ip_port_data.drop_duplicates()

        print(f"After removing duplicates and missing data: {len(ip_port_data)} unique IP:port combinations")

        # Create ip:port format
        ip_port_list = []
        for _, row in ip_port_data.iterrows():
            ip = str(row['IP地址']).strip()
            port = str(int(row['端口号']))  # Convert to int first to remove decimal, then to string
            ip_port_list.append(f"{ip}:{port}")

        # Sort the list for better organization
        ip_port_list.sort()

        # Write to output file
        output_file = 'sharepoint_target_ip.txt'
        with open(output_file, 'w', encoding='utf-8') as f:
            for ip_port in ip_port_list:
                f.write(ip_port + '\n')

        print(f"Successfully extracted {len(ip_port_list)} IP:port combinations to {output_file}")

        # Show first few entries as preview
        print("\nFirst 10 entries:")
        for i, ip_port in enumerate(ip_port_list[:10]):
            print(f"  {ip_port}")

        if len(ip_port_list) > 10:
            print(f"  ... and {len(ip_port_list) - 10} more entries")

        return True

    except FileNotFoundError:
        print("Error: 2.xlsx file not found")
        return False
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    success = extract_ip_port()
    sys.exit(0 if success else 1)